# Claude2API Troubleshooting Guide

This guide helps you diagnose and resolve common issues with Claude2API deployment and operation.

## 🚨 Quick Diagnostics

### System Check Commands
```bash
# Check if service is running
curl -I http://localhost:8080/health

# Check Docker status
docker-compose ps

# View recent logs
docker-compose logs --tail=50 claude2api

# Check system resources
docker stats claude2api
```

## 🐳 Docker-Related Issues

### Issue 1: Docker Not Found
**Symptoms:**
```
bash: docker: command not found
```

**Solutions:**
1. **Install Docker Desktop** (Mac/Windows):
   - Download from [docker.com](https://www.docker.com/products/docker-desktop)
   - Follow installation instructions for your platform

2. **Install Docker Engine** (Linux):
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose
   
   # CentOS/RHEL
   sudo yum install docker docker-compose
   ```

3. **Alternative: Use source installation** (see DEPLOYMENT.md)

### Issue 2: Mac M2 Platform Issues
**Symptoms:**
```
no matching manifest for linux/arm64/v8 in the manifest list entries
WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8)
```

**Solutions:**
1. **Force ARM64 platform:**
   ```bash
   docker-compose down
   docker-compose up -d --platform linux/arm64
   ```

2. **Use AMD64 emulation:**
   ```bash
   docker-compose down
   docker-compose up -d --platform linux/amd64
   ```

3. **Update Docker Desktop** to latest version with better ARM64 support

### Issue 3: Port Already in Use
**Symptoms:**
```
Error starting userland proxy: listen tcp4 0.0.0.0:8080: bind: address already in use
```

**Solutions:**
1. **Find and kill the process:**
   ```bash
   # Find process using port 8080
   lsof -i :8080
   # Kill the process (replace PID)
   kill -9 <PID>
   ```

2. **Change port in docker-compose.yml:**
   ```yaml
   ports:
     - "8081:8080"  # Use port 8081 instead
   ```

3. **Stop conflicting services:**
   ```bash
   # Common conflicting services
   sudo systemctl stop nginx
   sudo systemctl stop apache2
   ```

### Issue 4: Container Keeps Restarting
**Symptoms:**
```
claude2api    /app/main    Restarting (1) 5 seconds ago
```

**Solutions:**
1. **Check logs for errors:**
   ```bash
   docker-compose logs claude2api
   ```

2. **Common fixes:**
   - Verify session keys are valid
   - Check API key format
   - Ensure environment variables are properly set

3. **Remove restart policy temporarily:**
   ```yaml
   # Comment out in docker-compose.yml
   # restart: unless-stopped
   ```

## 🔑 Authentication Issues

### Issue 5: Invalid API Key
**Symptoms:**
```json
{
  "error": {
    "message": "Invalid API key",
    "type": "authentication_error"
  }
}
```

**Solutions:**
1. **Verify API key in request:**
   ```bash
   curl -H "Authorization: Bearer luoshui123" \
        http://localhost:8080/v1/chat/completions
   ```

2. **Check docker-compose.yml configuration:**
   ```yaml
   environment:
     - APIKEY=luoshui123  # Make sure this matches your requests
   ```

3. **Update API key:**
   ```bash
   # Edit docker-compose.yml, then restart
   docker-compose down && docker-compose up -d
   ```

### Issue 6: Session Key Problems
**Symptoms:**
```json
{
  "error": {
    "message": "Invalid session key format",
    "type": "invalid_request_error"
  }
}
```

**Solutions:**
1. **Verify session key format:**
   - Must start with `sk-ant-sid01-`
   - Should be base64-encoded after the prefix
   - No extra spaces or newlines

2. **Test session keys manually:**
   - Log into Claude web interface
   - Check if session is still active
   - Generate new session keys if needed

3. **Update session keys in configuration:**
   ```yaml
   environment:
     - SESSIONS=sk-ant-sid01-valid-key1,sk-ant-sid01-valid-key2
   ```

## 🌐 Network and Connectivity Issues

### Issue 7: Cannot Reach Claude API
**Symptoms:**
```
dial tcp: lookup claude.ai: no such host
connection timeout
```

**Solutions:**
1. **Check internet connectivity:**
   ```bash
   ping google.com
   nslookup claude.ai
   ```

2. **Configure proxy if needed:**
   ```yaml
   environment:
     - PROXY=http://your-proxy:8080
   ```

3. **Check firewall settings:**
   ```bash
   # Allow outbound HTTPS
   sudo ufw allow out 443
   ```

### Issue 8: Service Not Accessible
**Symptoms:**
```
curl: (7) Failed to connect to localhost port 8080: Connection refused
```

**Solutions:**
1. **Check if container is running:**
   ```bash
   docker-compose ps
   ```

2. **Verify port mapping:**
   ```bash
   docker port claude2api
   ```

3. **Check service binding:**
   ```yaml
   environment:
     - ADDRESS=0.0.0.0:8080  # Not 127.0.0.1:8080
   ```

## 💾 Resource Issues

### Issue 9: High Memory Usage
**Symptoms:**
```
Container killed due to memory limit
OOMKilled
```

**Solutions:**
1. **Add memory limits:**
   ```yaml
   services:
     claude2api:
       deploy:
         resources:
           limits:
             memory: 512M
           reservations:
             memory: 256M
   ```

2. **Monitor memory usage:**
   ```bash
   docker stats claude2api
   ```

3. **Reduce chat history length:**
   ```yaml
   environment:
     - MAX_CHAT_HISTORY_LENGTH=5000  # Reduce from 10000
   ```

### Issue 10: Disk Space Issues
**Symptoms:**
```
no space left on device
```

**Solutions:**
1. **Clean Docker resources:**
   ```bash
   docker system prune -a
   docker volume prune
   ```

2. **Rotate logs:**
   ```bash
   # Add to docker-compose.yml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

## 🔧 Configuration Issues

### Issue 11: Environment Variables Not Working
**Symptoms:**
- Service starts but uses default values
- Configuration changes don't take effect

**Solutions:**
1. **Check variable format:**
   ```yaml
   environment:
     - APIKEY=value  # Correct
     # - APIKEY: value  # Wrong format for docker-compose
   ```

2. **Restart after changes:**
   ```bash
   docker-compose down && docker-compose up -d
   ```

3. **Use config.yaml instead:**
   ```yaml
   # Create config.yaml in project root
   apiKey: "your-key"
   sessions:
     - sessionKey: "sk-ant-sid01-xxx"
   ```

### Issue 12: YAML Configuration Not Loading
**Symptoms:**
- config.yaml exists but environment variables are used
- Configuration file ignored

**Solutions:**
1. **Check file location:**
   ```bash
   # Must be in container's /app directory
   ls -la config.yaml
   ```

2. **Mount config file:**
   ```yaml
   services:
     claude2api:
       volumes:
         - ./config.yaml:/app/config.yaml:ro
   ```

3. **Verify YAML syntax:**
   ```bash
   # Check YAML syntax
   python -c "import yaml; yaml.safe_load(open('config.yaml'))"
   ```

## 🔍 Debugging Steps

### Step 1: Enable Debug Logging
```yaml
environment:
  - DEBUG=true
  - LOG_LEVEL=debug
```

### Step 2: Check Service Health
```bash
# Health endpoint
curl http://localhost:8080/health

# Test API endpoint
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer luoshui123" \
  -d '{"model":"claude-3-7-sonnet-20250219","messages":[{"role":"user","content":"test"}]}'
```

### Step 3: Analyze Logs
```bash
# Real-time logs
docker-compose logs -f claude2api

# Search for specific errors
docker-compose logs claude2api | grep -i error

# Export logs for analysis
docker-compose logs claude2api > claude2api.log
```

### Step 4: Container Inspection
```bash
# Inspect container
docker inspect claude2api

# Execute commands inside container
docker-compose exec claude2api sh

# Check processes inside container
docker-compose exec claude2api ps aux
```

## 🆘 Getting Help

### Before Asking for Help
1. **Check logs** for specific error messages
2. **Try basic troubleshooting** steps above
3. **Test with minimal configuration**
4. **Document your environment** (OS, Docker version, etc.)

### Information to Include
- Operating system and architecture
- Docker and docker-compose versions
- Complete error messages from logs
- Your docker-compose.yml configuration (remove sensitive data)
- Steps to reproduce the issue

### Resources
- **GitHub Issues**: [Project Repository](https://github.com/yushangxiao/claude2api/issues)
- **Documentation**: README.md and docs/ folder
- **Community**: Check existing issues for similar problems

## 🔄 Recovery Procedures

### Complete Reset
```bash
# Stop and remove everything
docker-compose down -v
docker system prune -a

# Start fresh
docker-compose up -d
```

### Backup and Restore
```bash
# Backup configuration
cp docker-compose.yml docker-compose.yml.backup
cp config.yaml config.yaml.backup

# Restore from backup
cp docker-compose.yml.backup docker-compose.yml
docker-compose up -d
```

### Emergency Fallback
If Docker deployment fails completely, use source installation:
```bash
# Install Go and build from source
go build -o claude2api main.go
./claude2api
```

Remember: Most issues are configuration-related. Double-check your environment variables, session keys, and network connectivity before diving into complex debugging.
