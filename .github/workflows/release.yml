name: Publish Release  

on:  
  push:  
    tags:  
      - 'v*' # 当推送以 "v" 开头的标签时触发（如 v1.0.0, v2.1.0）  

jobs:  
  update-release-draft:  
    permissions:  
      contents: write  
      pull-requests: write  
    runs-on: ubuntu-latest  
    steps:  
      # Step 1: 检出代码库  
      - name: Checkout code  
        uses: actions/checkout@v3  

      # Step 2: 获取项目名称  
      - name: Get Project Name  
        id: project_info  
        run: |  
          REPO_NAME=${GITHUB_REPOSITORY#*/}  
          echo "repo_name=$REPO_NAME" >> $GITHUB_OUTPUT  
          echo "Using repository name: $REPO_NAME"  

      # Step 3: 自动生成 Release  
      - name: Create Release  
        id: create_release  
        uses: actions/create-release@v1  
        env:  
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  
        with:  
          tag_name: ${{ github.ref_name }}  
          release_name: ${{ github.ref_name }}  
          draft: false  
          prerelease: false  
      
      # Step 4: 构建zip文件  
      - name: Create ZIP file  
        run: |  
          zip -r ${{ steps.project_info.outputs.repo_name }}.zip . -x "*.git*" "*.github*" "*.env*" "logs/*" "tests/*"  

      # Step 5: 上传构建文件  
      - name: Upload Release Asset  
        uses: actions/upload-release-asset@v1  
        env:  
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  
        with:  
          upload_url: ${{ steps.create_release.outputs.upload_url }}  
          asset_path: ./${{ steps.project_info.outputs.repo_name }}.zip  
          asset_name: ${{ steps.project_info.outputs.repo_name }}.zip  
          asset_content_type: application/zip  
