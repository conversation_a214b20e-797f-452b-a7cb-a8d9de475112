# Claude2API Deployment Guide

This guide provides comprehensive instructions for deploying Claude2API on various platforms, with special attention to Mac M2 (Apple Silicon) compatibility.

## 🏗️ System Requirements

### Minimum Requirements
- **Memory**: 512MB RAM
- **Storage**: 100MB free space
- **Network**: Internet connection for API calls

### Platform Support
- **Docker**: Linux, macOS (Intel/Apple Silicon), Windows
- **Source Build**: Any platform with Go 1.22.2+ support
- **Pre-built Binaries**: Available for major platforms

## 🚀 Deployment Methods

### Method 1: Docker Compose (Recommended)

#### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+

#### Quick Start
```bash
# Clone the repository (if not already done)
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api

# Start the service in detached mode
docker-compose up -d
```

#### Mac M2 (Apple Silicon) Compatibility
The Docker image `ghcr.io/yushangxiao/claude2api:latest` supports multi-architecture builds including ARM64 (Apple Silicon). If you encounter platform issues, you can force the platform:

```bash
# Force ARM64 platform for Apple Silicon
docker-compose up -d --platform linux/arm64

# Or force AMD64 if ARM64 has issues
docker-compose up -d --platform linux/amd64
```

#### Configuration
The `docker-compose.yml` file includes the following default configuration:
- **Port**: 8080 (mapped to host)
- **API Key**: `luoshui123` (change this!)
- **Sessions**: Pre-configured Claude session keys
- **Auto-restart**: Enabled

#### Managing the Service
```bash
# View logs
docker-compose logs -f

# Stop the service
docker-compose down

# Restart the service
docker-compose restart

# Update to latest image
docker-compose pull && docker-compose up -d
```

### Method 2: Source Installation

#### Prerequisites
- Go 1.22.2 or higher
- Git

#### Installation Steps
```bash
# Install Go (if not installed)
# macOS with Homebrew:
brew install go

# Ubuntu/Debian:
sudo apt update && sudo apt install golang-go

# Clone and build
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api

# Download dependencies
go mod download

# Build the application
go build -o claude2api main.go

# Run the application
./claude2api
```

#### Configuration for Source Installation
Create a `config.yaml` file in the project root:

```yaml
# Sessions configuration
sessions:
  - sessionKey: "sk-ant-sid01-your-session-key-1"
    orgID: ""
  - sessionKey: "sk-ant-sid01-your-session-key-2"
    orgID: ""

# Server configuration
address: "0.0.0.0:8080"
apiKey: "your-secure-api-key"

# Feature settings
chatDelete: true
maxChatHistoryLength: 10000
noRolePrefix: false
promptDisableArtifacts: true
enableMirrorApi: false
mirrorApiPrefix: "/mirror"
```

### Method 3: Docker Run (Alternative)

```bash
docker run -d \
  --name claude2api \
  -p 8080:8080 \
  -e SESSIONS="sk-ant-sid01-key1,sk-ant-sid01-key2" \
  -e APIKEY="your-secure-api-key" \
  -e CHAT_DELETE=true \
  -e MAX_CHAT_HISTORY_LENGTH=10000 \
  -e NO_ROLE_PREFIX=false \
  -e PROMPT_DISABLE_ARTIFACTS=true \
  -e ENABLE_MIRROR_API=false \
  -e MIRROR_API_PREFIX="/mirror" \
  --restart unless-stopped \
  ghcr.io/yushangxiao/claude2api:latest
```

## ⚙️ Configuration Reference

### Environment Variables
| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SESSIONS` | Comma-separated Claude session keys | - | Yes |
| `ADDRESS` | Server bind address | `0.0.0.0:8080` | No |
| `APIKEY` | API authentication key | - | Yes |
| `PROXY` | HTTP proxy URL | - | No |
| `CHAT_DELETE` | Auto-delete chats after use | `true` | No |
| `MAX_CHAT_HISTORY_LENGTH` | Max chat history before file upload | `10000` | No |
| `NO_ROLE_PREFIX` | Disable role prefixes in messages | `false` | No |
| `PROMPT_DISABLE_ARTIFACTS` | Try to disable Claude artifacts | `false` | No |
| `ENABLE_MIRROR_API` | Allow direct session key usage | `false` | No |
| `MIRROR_API_PREFIX` | Prefix for mirror API protection | - | No |

### Session Key Format
Claude session keys should follow this format:
```
sk-ant-sid01-[base64-encoded-session-data]
```

### Security Considerations
1. **Change the default API key** from `luoshui123` to a secure value
2. **Protect session keys** - these provide access to Claude accounts
3. **Use HTTPS** in production environments
4. **Restrict network access** to trusted sources only

## 🔧 Verification and Testing

### Health Check
```bash
# Check if the service is running
curl http://localhost:8080/health

# Test API endpoint
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "claude-3-7-sonnet-********",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

### Log Monitoring
```bash
# Docker Compose logs
docker-compose logs -f claude2api

# Docker container logs
docker logs -f claude2api

# Source installation logs
# Logs are printed to stdout/stderr
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Docker Platform Issues (Mac M2)
**Problem**: `no matching manifest for linux/arm64/v8`
**Solution**:
```bash
# Try forcing AMD64 platform
docker-compose down
docker-compose up -d --platform linux/amd64
```

#### 2. Port Already in Use
**Problem**: `bind: address already in use`
**Solution**:
```bash
# Find process using port 8080
lsof -i :8080
# Kill the process or change port in docker-compose.yml
```

#### 3. Session Key Issues
**Problem**: `Invalid session key format`
**Solution**:
- Ensure session keys start with `sk-ant-sid01-`
- Check for extra spaces or newlines
- Verify keys are still valid in Claude web interface

#### 4. Memory Issues
**Problem**: Container crashes or high memory usage
**Solution**:
```bash
# Add memory limits to docker-compose.yml
services:
  claude2api:
    # ... other config
    deploy:
      resources:
        limits:
          memory: 512M
```

#### 5. Network Connectivity
**Problem**: Cannot reach Claude API
**Solution**:
- Check internet connection
- Verify proxy settings if using corporate network
- Test DNS resolution: `nslookup claude.ai`

### Debug Mode
Enable verbose logging by setting environment variable:
```bash
export DEBUG=true
./claude2api
```

## 📊 Monitoring and Maintenance

### Performance Monitoring
- Monitor memory usage: `docker stats claude2api`
- Check response times in application logs
- Monitor disk space for log files

### Regular Maintenance
1. **Update the image** monthly: `docker-compose pull && docker-compose up -d`
2. **Rotate logs** to prevent disk space issues
3. **Monitor session key validity** - replace expired keys
4. **Review security settings** periodically

### Backup and Recovery
- **Configuration**: Backup `docker-compose.yml` and `config.yaml`
- **Session Keys**: Store securely in password manager
- **No persistent data** - service is stateless

## 🔗 Next Steps

After successful deployment:
1. Review the [API Usage Guide](USAGE.md)
2. Set up monitoring and alerting
3. Configure reverse proxy (nginx/traefik) for production
4. Implement SSL/TLS certificates
5. Set up log aggregation if needed

For additional support, refer to the project's GitHub repository or documentation.
