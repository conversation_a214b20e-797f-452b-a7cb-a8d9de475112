# Claude2Api
Transform <PERSON>'s web service into an API service, supporting image recognition, file upload, streaming transmission, thing output... 
The API supports access in the OpenAI format.

[![Go Report Card](https://goreportcard.com/badge/github.com/yushangxiao/claude2api)](https://goreportcard.com/report/github.com/yushangxiao/claude2api)
[![License](https://img.shields.io/github/license/yushangxiao/claude2api)](LICENSE)
|[中文](https://github.com/yushangxiao/claude2api/blob/main/docs/chinses.md)


NOTICE: ONLY PRO USER CAN USE ALL MODELS , FREE USER ONLY CAN USE claude-sonnet-4-20250514

## ✨ Features

- 🖼️ **Image Recognition** - Send images to <PERSON> for analysis
- 📝 **Automatic Conversation Management** -  Conversation can be automatically deleted after use
- 🌊 **Streaming Responses** - Get real-time streaming outputs from <PERSON>
- 📁 **File Upload Support** - Upload long context
- 🧠 **Thinking Process** - Access Claude's step-by-step reasoning, support <think>
- 🔄 **Chat History Management** - Control the length of conversation context , exceeding will upload file
- 🌐 **Proxy Support** - Route requests through your preferred proxy
- 🔐 **API Key Authentication** - Secure your API endpoints
- 🔁 **Automatic Retry** - Feature to automatically retry requests when request fail
- 🌐 **Direct Proxy** -let sk-ant-sid01* as key to use

## 📋 Prerequisites

- Go 1.23+ (for building from source)
- Docker (for containerized deployment)

## 🚀 Deployment Options

### Docker

```bash
docker run -d \
  -p 8080:8080 \
  -e SESSIONS=sk-ant-sid01-xxxx,sk-ant-sid01-yyyy \
  -e APIKEY=123 \
  -e CHAT_DELETE=true \
  -e MAX_CHAT_HISTORY_LENGTH=10000 \
  -e NO_ROLE_PREFIX=false \
  -e PROMPT_DISABLE_ARTIFACTS=false \
  -e ENABLE_MIRROR_API=false \
  -e MIRROR_API_PREFIX=/mirror \
  --name claude2api \
  ghcr.io/yushangxiao/claude2api:latest
```

### Docker Compose

Create a `docker-compose.yml` file:

```yaml
version: '3'
services:
  claude2api:
    image: ghcr.io/yushangxiao/claude2api:latest
    container_name: claude2api
    ports:
      - "8080:8080"
    environment:
      - SESSIONS=sk-ant-sid01-xxxx,sk-ant-sid01-yyyy
      - ADDRESS=0.0.0.0:8080
      - APIKEY=123
      - PROXY=http://proxy:2080  # Optional
      - CHAT_DELETE=true
      - MAX_CHAT_HISTORY_LENGTH=10000
      - NO_ROLE_PREFIX=false
      - PROMPT_DISABLE_ARTIFACTS=true
      - ENABLE_MIRROR_API=false
      - MIRROR_API_PREFIX=/mirror
    restart: unless-stopped

```

Then run:

```bash
docker-compose up -d
```

### Hugging Face Spaces

You can deploy this project to Hugging Face Spaces with Docker:

1. Fork the Hugging Face Space at [https://huggingface.co/spaces/rclon/claude2api](https://huggingface.co/spaces/rclon/claude2api)
2. Configure your environment variables in the Settings tab
3. The Space will automatically  deploy the Docker image

notice: In Hugging Face, /v1 might be blocked, you can use /hf/v1 instead.
### Direct Deployment

```bash
# Clone the repository
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api
cp .env.example .env  
vim .env  
# Build the binary
go build -o claude2api .

./claude2api
```

## ⚙️ Configuration

### YAML Configuration

You can configure Claude2API using a `config.yaml` file in the application's root directory. If this file exists, it will be used instead of environment variables.

Example `config.yaml`:

```yaml
# Sessions configuration
sessions:
  - sessionKey: "sk-ant-sid01-xxxx"
    orgID: ""
  - sessionKey: "sk-ant-sid01-yyyy"
    orgID: ""

# Server address
address: "0.0.0.0:8080"

# API authentication key
apiKey: "123"

# Other configuration options...
chatDelete: true
maxChatHistoryLength: 10000
noRolePrefix: false
promptDisableArtifacts: false
enableMirrorApi: false
mirrorApiPrefix: ""
```

A sample configuration file is provided as `config.yaml.example` in the repository.

### Environment Variables

If `config.yaml` doesn't exist, the application will use environment variables for configuration:

| Environment Variable | Description | Default |
|----------------------|-------------|---------|
| `SESSIONS` | Comma-separated list of Claude API session keys | Required |
| `ADDRESS` | Server address and port | `0.0.0.0:8080` |
| `APIKEY` | API key for authentication | Required |
| `PROXY` | HTTP proxy URL | Optional |
| `CHAT_DELETE` | Whether to delete chat sessions after use | `true` |
| `MAX_CHAT_HISTORY_LENGTH` | Exceeding will text to file | `10000` |
| `NO_ROLE_PREFIX` | Do not add role in every message | `false` |
| `PROMPT_DISABLE_ARTIFACTS` | Add Prompt try to disable Artifacts | `false` |
| `ENABLE_MIRROR_API` | Enable direct use sk-ant-* as key | `false` |
| `MIRROR_API_PREFIX` | Add Prefix to protect Mirror，required when ENABLE_MIRROR_API is true | `` |


## 📝 API Usage

### Authentication

Include your API key in the request header:

```
Authorization: Bearer YOUR_API_KEY
```

### Chat Completion

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Hello, Claude!"
      }
    ],
    "stream": true
  }'
```

### Image Analysis

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What\'s in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/jpeg;base64,..."
            }
          }
        ]
      }
    ]
  }'
```


## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Anthropic](https://www.anthropic.com/) for creating Claude
- The Go community for the amazing ecosystem

---
 ## 🎁 Support

If you find this project helpful, consider supporting me on [Afdian](https://afdian.com/a/iscoker)  😘

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=yushangxiao/claude2api&type=Date)](https://www.star-history.com/#yushangxiao/claude2api&Date)

Made with ❤️ by [yushangxiao](https://github.com/yushangxiao)

---

## 📚 Comprehensive Documentation

This repository now includes detailed documentation for deployment and operations:

### 📖 Documentation Files
- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Complete deployment guide with Docker and source installation
- **[USAGE.md](USAGE.md)** - API usage guide with examples and best practices
- **[TROUBLESHOOTING.md](TROUBLESHOOTING.md)** - Common issues and solutions

### 🚀 Quick Start (Updated)

#### Option 1: Docker Compose (Recommended)
```bash
# Clone and start
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api

# Start in detached mode
docker-compose up -d

# Check status
docker-compose ps
```

#### Option 2: Mac M2 (Apple Silicon) Compatible
If you encounter platform issues on Apple Silicon:
```bash
# Force ARM64 platform
docker-compose up -d --platform linux/arm64

# Or use AMD64 emulation if needed
docker-compose up -d --platform linux/amd64
```

#### Option 3: Source Installation Fallback
If Docker is not available:
```bash
# Install Go 1.22.2+ first, then:
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api
go mod download
go build -o claude2api main.go
./claude2api
```

### 🔧 Configuration Quick Reference
The `docker-compose.yml` includes these key settings:
- **Port**: 8080 (change if needed)
- **API Key**: `luoshui123` (⚠️ **Change this for security!**)
- **Sessions**: Pre-configured Claude session keys
- **Auto-restart**: Enabled for reliability

### ✅ Verification
Test your deployment:
```bash
# Health check
curl http://localhost:8080/health

# API test
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer luoshui123" \
  -d '{"model":"claude-3-7-sonnet-20250219","messages":[{"role":"user","content":"Hello!"}]}'
```

### 🆘 Need Help?
- **Deployment issues**: See [DEPLOYMENT.md](DEPLOYMENT.md)
- **API usage questions**: See [USAGE.md](USAGE.md)
- **Problems or errors**: See [TROUBLESHOOTING.md](TROUBLESHOOTING.md)

### 🔒 Security Notice
**Important**: Change the default API key (`luoshui123`) to a secure value before production use!

---

## 📚 中文文档 / Chinese Documentation

本仓库现在包含详细的中文部署和操作文档：

### 📖 中文文档文件
- **[DEPLOYMENT_CN.md](DEPLOYMENT_CN.md)** - 完整的部署指南，包含 Docker 和源码安装
- **[USAGE_CN.md](USAGE_CN.md)** - API 使用指南，包含示例和最佳实践
- **[TROUBLESHOOTING_CN.md](TROUBLESHOOTING_CN.md)** - 常见问题和解决方案
- **[DEPLOYMENT_SUMMARY_CN.md](DEPLOYMENT_SUMMARY_CN.md)** - 部署总结和状态概览

### 🚀 中文快速开始

#### 选项 1: Docker Compose（推荐）
```bash
# 克隆并启动
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api

# 以分离模式启动
docker-compose up -d

# 检查状态
docker-compose ps
```

#### 选项 2: Mac M2 (Apple Silicon) 兼容
如果在 Apple Silicon 上遇到平台问题：
```bash
# 强制使用 ARM64 平台
docker-compose up -d --platform linux/arm64

# 或者如果需要使用 AMD64 模拟
docker-compose up -d --platform linux/amd64
```

#### 选项 3: 源码安装备用方案
如果 Docker 不可用：
```bash
# 首先安装 Go 1.22.2+，然后：
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api
go mod download
go build -o claude2api main.go
./claude2api
```

### 🔧 配置快速参考
`docker-compose.yml` 包含这些关键设置：
- **端口**: 8080（如需要可更改）
- **API 密钥**: `luoshui123` (⚠️ **为了安全请更改此密钥！**)
- **会话**: 预配置的 Claude 会话密钥
- **自动重启**: 为可靠性而启用

### ✅ 验证部署
测试您的部署：
```bash
# 健康检查
curl http://localhost:8080/health

# API 测试
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer luoshui123" \
  -d '{"model":"claude-3-7-sonnet-20250219","messages":[{"role":"user","content":"你好！"}]}'
```

### 🆘 需要帮助？
- **部署问题**: 查看 [DEPLOYMENT_CN.md](DEPLOYMENT_CN.md)
- **API 使用问题**: 查看 [USAGE_CN.md](USAGE_CN.md)
- **问题或错误**: 查看 [TROUBLESHOOTING_CN.md](TROUBLESHOOTING_CN.md)
- **部署状态概览**: 查看 [DEPLOYMENT_SUMMARY_CN.md](DEPLOYMENT_SUMMARY_CN.md)
