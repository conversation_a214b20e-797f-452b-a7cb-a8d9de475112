version: '3'
services:
  claude2api:
    image: ghcr.io/yushangxiao/claude2api:latest
    container_name: claude2api
    ports:
      - "8080:8080"
    environment:
      - SESSIONS=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,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,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,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
      - ADDRESS=0.0.0.0:8080
      - APIKEY=luoshui123
      - CHAT_DELETE=true
      - MAX_CHAT_HISTORY_LENGTH=10000
      - NO_ROLE_PREFIX=false
      - PROMPT_DISABLE_ARTIFACTS=true
      - ENABLE_MIRROR_API=false
      - MIRROR_API_PREFIX=/mirror
    restart: unless-stopped