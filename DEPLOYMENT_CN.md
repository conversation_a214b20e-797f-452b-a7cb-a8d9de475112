# Claude2API 部署指南

本指南提供了在各种平台上部署 Claude2API 的全面说明，特别关注 Mac M2 (Apple Silicon) 兼容性。

## 🏗️ 系统要求

### 最低要求
- **内存**: 512MB RAM
- **存储**: 100MB 可用空间
- **网络**: 用于 API 调用的互联网连接

### 平台支持
- **Docker**: Linux, macOS (Intel/Apple Silicon), Windows
- **源码构建**: 任何支持 Go 1.22.2+ 的平台
- **预构建二进制文件**: 适用于主要平台

## 🚀 部署方法

### 方法 1: Docker Compose（推荐）

#### 先决条件
- Docker Engine 20.10+
- Docker Compose 2.0+

#### 快速开始
```bash
# 克隆仓库（如果尚未完成）
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api

# 以分离模式启动服务
docker-compose up -d
```

#### Mac M2 (Apple Silicon) 兼容性
Docker 镜像 `ghcr.io/yushangxiao/claude2api:latest` 支持多架构构建，包括 ARM64 (Apple Silicon)。如果遇到平台问题，可以强制指定平台：

```bash
# 为 Apple Silicon 强制使用 ARM64 平台
docker-compose up -d --platform linux/arm64

# 或者如果 ARM64 有问题则强制使用 AMD64
docker-compose up -d --platform linux/amd64
```

#### 配置
`docker-compose.yml` 文件包含以下默认配置：
- **端口**: 8080（映射到主机）
- **API 密钥**: `luoshui123`（请更改此密钥！）
- **会话**: 预配置的 Claude 会话密钥
- **自动重启**: 已启用

#### 管理服务
```bash
# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 更新到最新镜像
docker-compose pull && docker-compose up -d
```

### 方法 2: 源码安装

#### 先决条件
- Go 1.22.2 或更高版本
- Git

#### 安装步骤
```bash
# 安装 Go（如果未安装）
# macOS 使用 Homebrew:
brew install go

# Ubuntu/Debian:
sudo apt update && sudo apt install golang-go

# 克隆并构建
git clone https://github.com/yushangxiao/claude2api.git
cd claude2api

# 下载依赖
go mod download

# 构建应用程序
go build -o claude2api main.go

# 运行应用程序
./claude2api
```

#### 源码安装的配置
在项目根目录创建 `config.yaml` 文件：

```yaml
# 会话配置
sessions:
  - sessionKey: "sk-ant-sid01-your-session-key-1"
    orgID: ""
  - sessionKey: "sk-ant-sid01-your-session-key-2"
    orgID: ""

# 服务器配置
address: "0.0.0.0:8080"
apiKey: "your-secure-api-key"

# 功能设置
chatDelete: true
maxChatHistoryLength: 10000
noRolePrefix: false
promptDisableArtifacts: true
enableMirrorApi: false
mirrorApiPrefix: "/mirror"
```

### 方法 3: Docker Run（替代方案）

```bash
docker run -d \
  --name claude2api \
  -p 8080:8080 \
  -e SESSIONS="sk-ant-sid01-key1,sk-ant-sid01-key2" \
  -e APIKEY="your-secure-api-key" \
  -e CHAT_DELETE=true \
  -e MAX_CHAT_HISTORY_LENGTH=10000 \
  -e NO_ROLE_PREFIX=false \
  -e PROMPT_DISABLE_ARTIFACTS=true \
  -e ENABLE_MIRROR_API=false \
  -e MIRROR_API_PREFIX="/mirror" \
  --restart unless-stopped \
  ghcr.io/yushangxiao/claude2api:latest
```

## ⚙️ 配置参考

### 环境变量
| 变量 | 描述 | 默认值 | 必需 |
|------|------|--------|------|
| `SESSIONS` | 逗号分隔的 Claude 会话密钥 | - | 是 |
| `ADDRESS` | 服务器绑定地址 | `0.0.0.0:8080` | 否 |
| `APIKEY` | API 认证密钥 | - | 是 |
| `PROXY` | HTTP 代理 URL | - | 否 |
| `CHAT_DELETE` | 使用后自动删除聊天 | `true` | 否 |
| `MAX_CHAT_HISTORY_LENGTH` | 文件上传前的最大聊天历史 | `10000` | 否 |
| `NO_ROLE_PREFIX` | 在消息中禁用角色前缀 | `false` | 否 |
| `PROMPT_DISABLE_ARTIFACTS` | 尝试禁用 Claude artifacts | `false` | 否 |
| `ENABLE_MIRROR_API` | 允许直接使用会话密钥 | `false` | 否 |
| `MIRROR_API_PREFIX` | 镜像 API 保护前缀 | - | 否 |

### 会话密钥格式
Claude 会话密钥应遵循此格式：
```
sk-ant-sid01-[base64-encoded-session-data]
```

### 安全考虑
1. **更改默认 API 密钥** 从 `luoshui123` 改为安全值
2. **保护会话密钥** - 这些提供对 Claude 账户的访问
3. **在生产环境中使用 HTTPS**
4. **限制网络访问** 仅限可信来源

## 🔧 验证和测试

### 健康检查
```bash
# 检查服务是否运行
curl http://localhost:8080/health

# 测试 API 端点
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'
```

### 日志监控
```bash
# Docker Compose 日志
docker-compose logs -f claude2api

# Docker 容器日志
docker logs -f claude2api

# 源码安装日志
# 日志打印到 stdout/stderr
```

## 🚨 故障排除

### 常见问题

#### 1. Docker 平台问题（Mac M2）
**问题**: `no matching manifest for linux/arm64/v8`
**解决方案**:
```bash
# 尝试强制使用 AMD64 平台
docker-compose down
docker-compose up -d --platform linux/amd64
```

#### 2. 端口已被使用
**问题**: `bind: address already in use`
**解决方案**:
```bash
# 查找使用端口 8080 的进程
lsof -i :8080
# 终止进程或在 docker-compose.yml 中更改端口
```

#### 3. 会话密钥问题
**问题**: `Invalid session key format`
**解决方案**:
- 确保会话密钥以 `sk-ant-sid01-` 开头
- 检查是否有额外的空格或换行符
- 在 Claude 网页界面中验证密钥是否仍然有效

#### 4. 内存问题
**问题**: 容器崩溃或高内存使用
**解决方案**:
```bash
# 在 docker-compose.yml 中添加内存限制
services:
  claude2api:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 512M
```

#### 5. 网络连接
**问题**: 无法访问 Claude API
**解决方案**:
- 检查互联网连接
- 如果使用企业网络，验证代理设置
- 测试 DNS 解析: `nslookup claude.ai`

### 调试模式
通过设置环境变量启用详细日志记录：
```bash
export DEBUG=true
./claude2api
```

## 📊 监控和维护

### 性能监控
- 监控内存使用: `docker stats claude2api`
- 检查应用程序日志中的响应时间
- 监控日志文件的磁盘空间

### 定期维护
1. **每月更新镜像**: `docker-compose pull && docker-compose up -d`
2. **轮换日志** 以防止磁盘空间问题
3. **监控会话密钥有效性** - 替换过期密钥
4. **定期检查安全设置**

### 备份和恢复
- **配置**: 备份 `docker-compose.yml` 和 `config.yaml`
- **会话密钥**: 安全存储在密码管理器中
- **无持久数据** - 服务是无状态的

## 🔗 下一步

成功部署后：
1. 查看 [API 使用指南](USAGE.md)
2. 设置监控和警报
3. 为生产配置反向代理（nginx/traefik）
4. 实施 SSL/TLS 证书
5. 如需要，设置日志聚合

如需额外支持，请参考项目的 GitHub 仓库或文档。
