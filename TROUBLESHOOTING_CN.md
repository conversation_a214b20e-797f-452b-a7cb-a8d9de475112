# Claude2API 故障排除指南

本指南帮助您诊断和解决 Claude2API 部署和操作中的常见问题。

## 🚨 快速诊断

### 系统检查命令
```bash
# 检查服务是否运行
curl -I http://localhost:8080/health

# 检查 Docker 状态
docker-compose ps

# 查看最近日志
docker-compose logs --tail=50 claude2api

# 检查系统资源
docker stats claude2api
```

## 🐳 Docker 相关问题

### 问题 1: Docker 未找到
**症状:**
```
bash: docker: command not found
```

**解决方案:**
1. **安装 Docker Desktop** (Mac/Windows):
   - 从 [docker.com](https://www.docker.com/products/docker-desktop) 下载
   - 按照您平台的安装说明操作

2. **安装 Docker Engine** (Linux):
   ```bash
   # Ubuntu/Debian
   sudo apt update
   sudo apt install docker.io docker-compose
   
   # CentOS/RHEL
   sudo yum install docker docker-compose
   ```

3. **替代方案: 使用源码安装** (参见 DEPLOYMENT.md)

### 问题 2: Mac M2 平台问题
**症状:**
```
no matching manifest for linux/arm64/v8 in the manifest list entries
WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8)
```

**解决方案:**
1. **强制使用 ARM64 平台:**
   ```bash
   docker-compose down
   docker-compose up -d --platform linux/arm64
   ```

2. **使用 AMD64 模拟:**
   ```bash
   docker-compose down
   docker-compose up -d --platform linux/amd64
   ```

3. **更新 Docker Desktop** 到具有更好 ARM64 支持的最新版本

### 问题 3: 端口已被使用
**症状:**
```
Error starting userland proxy: listen tcp4 0.0.0.0:8080: bind: address already in use
```

**解决方案:**
1. **查找并终止进程:**
   ```bash
   # 查找使用端口 8080 的进程
   lsof -i :8080
   # 终止进程（替换 PID）
   kill -9 <PID>
   ```

2. **在 docker-compose.yml 中更改端口:**
   ```yaml
   ports:
     - "8081:8080"  # 使用端口 8081 代替
   ```

3. **停止冲突的服务:**
   ```bash
   # 常见冲突服务
   sudo systemctl stop nginx
   sudo systemctl stop apache2
   ```

### 问题 4: 容器持续重启
**症状:**
```
claude2api    /app/main    Restarting (1) 5 seconds ago
```

**解决方案:**
1. **检查日志中的错误:**
   ```bash
   docker-compose logs claude2api
   ```

2. **常见修复:**
   - 验证会话密钥是否有效
   - 检查 API 密钥格式
   - 确保环境变量设置正确

3. **临时移除重启策略:**
   ```yaml
   # 在 docker-compose.yml 中注释掉
   # restart: unless-stopped
   ```

## 🔑 认证问题

### 问题 5: 无效的 API 密钥
**症状:**
```json
{
  "error": {
    "message": "Invalid API key",
    "type": "authentication_error"
  }
}
```

**解决方案:**
1. **验证请求中的 API 密钥:**
   ```bash
   curl -H "Authorization: Bearer luoshui123" \
        http://localhost:8080/v1/chat/completions
   ```

2. **检查 docker-compose.yml 配置:**
   ```yaml
   environment:
     - APIKEY=luoshui123  # 确保这与您的请求匹配
   ```

3. **更新 API 密钥:**
   ```bash
   # 编辑 docker-compose.yml，然后重启
   docker-compose down && docker-compose up -d
   ```

### 问题 6: 会话密钥问题
**症状:**
```json
{
  "error": {
    "message": "Invalid session key format",
    "type": "invalid_request_error"
  }
}
```

**解决方案:**
1. **验证会话密钥格式:**
   - 必须以 `sk-ant-sid01-` 开头
   - 前缀后应为 base64 编码
   - 无额外空格或换行符

2. **手动测试会话密钥:**
   - 登录 Claude 网页界面
   - 检查会话是否仍然活跃
   - 如需要生成新的会话密钥

3. **在配置中更新会话密钥:**
   ```yaml
   environment:
     - SESSIONS=sk-ant-sid01-valid-key1,sk-ant-sid01-valid-key2
   ```

## 🌐 网络和连接问题

### 问题 7: 无法访问 Claude API
**症状:**
```
dial tcp: lookup claude.ai: no such host
connection timeout
```

**解决方案:**
1. **检查互联网连接:**
   ```bash
   ping google.com
   nslookup claude.ai
   ```

2. **如需要配置代理:**
   ```yaml
   environment:
     - PROXY=http://your-proxy:8080
   ```

3. **检查防火墙设置:**
   ```bash
   # 允许出站 HTTPS
   sudo ufw allow out 443
   ```

### 问题 8: 服务无法访问
**症状:**
```
curl: (7) Failed to connect to localhost port 8080: Connection refused
```

**解决方案:**
1. **检查容器是否运行:**
   ```bash
   docker-compose ps
   ```

2. **验证端口映射:**
   ```bash
   docker port claude2api
   ```

3. **检查服务绑定:**
   ```yaml
   environment:
     - ADDRESS=0.0.0.0:8080  # 不是 127.0.0.1:8080
   ```

## 💾 资源问题

### 问题 9: 高内存使用
**症状:**
```
Container killed due to memory limit
OOMKilled
```

**解决方案:**
1. **添加内存限制:**
   ```yaml
   services:
     claude2api:
       deploy:
         resources:
           limits:
             memory: 512M
           reservations:
             memory: 256M
   ```

2. **监控内存使用:**
   ```bash
   docker stats claude2api
   ```

3. **减少聊天历史长度:**
   ```yaml
   environment:
     - MAX_CHAT_HISTORY_LENGTH=5000  # 从 10000 减少
   ```

### 问题 10: 磁盘空间问题
**症状:**
```
no space left on device
```

**解决方案:**
1. **清理 Docker 资源:**
   ```bash
   docker system prune -a
   docker volume prune
   ```

2. **轮换日志:**
   ```bash
   # 添加到 docker-compose.yml
   logging:
     driver: "json-file"
     options:
       max-size: "10m"
       max-file: "3"
   ```

## 🔧 配置问题

### 问题 11: 环境变量不工作
**症状:**
- 服务启动但使用默认值
- 配置更改不生效

**解决方案:**
1. **检查变量格式:**
   ```yaml
   environment:
     - APIKEY=value  # 正确
     # - APIKEY: value  # docker-compose 错误格式
   ```

2. **更改后重启:**
   ```bash
   docker-compose down && docker-compose up -d
   ```

3. **改用 config.yaml:**
   ```yaml
   # 在项目根目录创建 config.yaml
   apiKey: "your-key"
   sessions:
     - sessionKey: "sk-ant-sid01-xxx"
   ```

### 问题 12: YAML 配置未加载
**症状:**
- config.yaml 存在但使用环境变量
- 配置文件被忽略

**解决方案:**
1. **检查文件位置:**
   ```bash
   # 必须在容器的 /app 目录中
   ls -la config.yaml
   ```

2. **挂载配置文件:**
   ```yaml
   services:
     claude2api:
       volumes:
         - ./config.yaml:/app/config.yaml:ro
   ```

3. **验证 YAML 语法:**
   ```bash
   # 检查 YAML 语法
   python -c "import yaml; yaml.safe_load(open('config.yaml'))"
   ```

## 🔍 调试步骤

### 步骤 1: 启用调试日志
```yaml
environment:
  - DEBUG=true
  - LOG_LEVEL=debug
```

### 步骤 2: 检查服务健康
```bash
# 健康端点
curl http://localhost:8080/health

# 测试 API 端点
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer luoshui123" \
  -d '{"model":"claude-3-7-sonnet-20250219","messages":[{"role":"user","content":"test"}]}'
```

### 步骤 3: 分析日志
```bash
# 实时日志
docker-compose logs -f claude2api

# 搜索特定错误
docker-compose logs claude2api | grep -i error

# 导出日志进行分析
docker-compose logs claude2api > claude2api.log
```

### 步骤 4: 容器检查
```bash
# 检查容器
docker inspect claude2api

# 在容器内执行命令
docker-compose exec claude2api sh

# 检查容器内进程
docker-compose exec claude2api ps aux
```

## 🆘 获取帮助

### 寻求帮助前
1. **检查日志** 获取具体错误消息
2. **尝试上述基本故障排除** 步骤
3. **使用最小配置测试**
4. **记录您的环境**（操作系统、Docker 版本等）

### 需要包含的信息
- 操作系统和架构
- Docker 和 docker-compose 版本
- 日志中的完整错误消息
- 您的 docker-compose.yml 配置（移除敏感数据）
- 重现问题的步骤

### 资源
- **GitHub Issues**: [项目仓库](https://github.com/yushangxiao/claude2api/issues)
- **文档**: README.md 和 docs/ 文件夹
- **社区**: 检查现有问题以寻找类似问题

## 🔄 恢复程序

### 完全重置
```bash
# 停止并移除所有内容
docker-compose down -v
docker system prune -a

# 重新开始
docker-compose up -d
```

### 备份和恢复
```bash
# 备份配置
cp docker-compose.yml docker-compose.yml.backup
cp config.yaml config.yaml.backup

# 从备份恢复
cp docker-compose.yml.backup docker-compose.yml
docker-compose up -d
```

### 紧急备用方案
如果 Docker 部署完全失败，使用源码安装：
```bash
# 安装 Go 并从源码构建
go build -o claude2api main.go
./claude2api
```

记住：大多数问题都与配置相关。在深入复杂调试之前，请仔细检查您的环境变量、会话密钥和网络连接。
