# Claude2API Usage Guide

This guide covers how to use Claude2API after successful deployment, including API endpoints, authentication, and advanced features.

## 🔐 Authentication

All API requests require authentication using the API key configured during deployment.

### Header Format
```
Authorization: Bearer YOUR_API_KEY
```

### Example with curl
```bash
curl -H "Authorization: Bearer luoshui123" \
     -H "Content-Type: application/json" \
     http://localhost:8080/v1/chat/completions
```

## 📡 API Endpoints

### Base URL
- **Local**: `http://localhost:8080`
- **Production**: `https://your-domain.com`

### Available Endpoints
- `POST /v1/chat/completions` - Main chat completion endpoint
- `GET /health` - Health check endpoint
- `POST /hf/v1/chat/completions` - Hugging Face compatible endpoint

## 💬 Chat Completions

### Basic Request
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Hello, <PERSON>!"
      }
    ]
  }'
```

### Streaming Response
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Tell me a story"
      }
    ],
    "stream": true
  }'
```

### Multi-turn Conversation
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "What is the capital of France?"
      },
      {
        "role": "assistant",
        "content": "The capital of France is Paris."
      },
      {
        "role": "user",
        "content": "What is its population?"
      }
    ]
  }'
```

## 🖼️ Image Analysis

Claude2API supports image analysis through base64-encoded images or URLs.

### Base64 Image
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What do you see in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
            }
          }
        ]
      }
    ]
  }'
```

### Image URL
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Describe this image"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg"
            }
          }
        ]
      }
    ]
  }'
```

## 📁 File Upload Support

For long context or document analysis, Claude2API supports file uploads.

### Text File Upload
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Please analyze this document: [file content will be automatically uploaded when context exceeds MAX_CHAT_HISTORY_LENGTH]"
      }
    ]
  }'
```

## 🧠 Thinking Process

Access Claude's step-by-step reasoning with the `<think>` tag support.

### Request with Thinking
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Solve this math problem step by step: What is 15% of 240?"
      }
    ],
    "thinking": true
  }'
```

## 🔄 Advanced Features

### Mirror API (Direct Session Key Usage)
When `ENABLE_MIRROR_API=true`, you can use Claude session keys directly:

```bash
curl -X POST http://localhost:8080/mirror/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-ant-sid01-your-session-key" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Hello!"
      }
    ]
  }'
```

### Custom Parameters
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Write a creative story"
      }
    ],
    "max_tokens": 1000,
    "temperature": 0.7,
    "stream": true
  }'
```

## 🎯 Supported Models

### Available Models
- `claude-3-7-sonnet-20250219` - Latest Sonnet model
- `claude-sonnet-4-20250514` - Available for all users
- `claude-3-haiku-20240307` - Fast, lightweight model
- `claude-3-opus-20240229` - Most capable model (Pro users only)

### Model Selection
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-haiku-20240307",
    "messages": [
      {
        "role": "user",
        "content": "Quick question: What is 2+2?"
      }
    ]
  }'
```

## 🔧 Response Format

### Standard Response
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "claude-3-7-sonnet-20250219",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### Streaming Response
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-3-7-sonnet-20250219","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-3-7-sonnet-20250219","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-3-7-sonnet-20250219","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 🐍 Python Example

```python
import requests
import json

def chat_with_claude(message, api_key="luoshui123", base_url="http://localhost:8080"):
    url = f"{base_url}/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "claude-3-7-sonnet-20250219",
        "messages": [
            {"role": "user", "content": message}
        ]
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# Usage
result = chat_with_claude("Hello, Claude!")
print(result["choices"][0]["message"]["content"])
```

## 🌐 JavaScript Example

```javascript
async function chatWithClaude(message, apiKey = "luoshui123", baseUrl = "http://localhost:8080") {
    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: "claude-3-7-sonnet-20250219",
            messages: [
                { role: "user", content: message }
            ]
        })
    });
    
    return await response.json();
}

// Usage
chatWithClaude("Hello, Claude!")
    .then(result => console.log(result.choices[0].message.content));
```

## ⚠️ Rate Limits and Best Practices

### Rate Limiting
- Requests are limited by Claude's session rate limits
- Multiple session keys provide load balancing
- Failed requests automatically retry with different sessions

### Best Practices
1. **Use appropriate models** - Haiku for simple tasks, Sonnet for complex ones
2. **Implement retry logic** in your applications
3. **Monitor token usage** to optimize costs
4. **Use streaming** for better user experience
5. **Cache responses** when appropriate

### Error Handling
```python
import requests

def safe_chat_request(message):
    try:
        response = requests.post(
            "http://localhost:8080/v1/chat/completions",
            headers={"Authorization": "Bearer luoshui123"},
            json={
                "model": "claude-3-7-sonnet-20250219",
                "messages": [{"role": "user", "content": message}]
            },
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
```

## 🔍 Monitoring Usage

### Health Check
```bash
curl http://localhost:8080/health
```

### Log Analysis
Monitor logs for:
- Request patterns
- Error rates
- Response times
- Session key usage

For more advanced usage patterns and integration examples, refer to the project's GitHub repository.
