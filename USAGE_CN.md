# Claude2API 使用指南

本指南涵盖了成功部署后如何使用 Claude2API，包括 API 端点、认证和高级功能。

## 🔐 认证

所有 API 请求都需要使用部署期间配置的 API 密钥进行认证。

### 请求头格式
```
Authorization: Bearer YOUR_API_KEY
```

### curl 示例
```bash
curl -H "Authorization: Bearer luoshui123" \
     -H "Content-Type: application/json" \
     http://localhost:8080/v1/chat/completions
```

## 📡 API 端点

### 基础 URL
- **本地**: `http://localhost:8080`
- **生产**: `https://your-domain.com`

### 可用端点
- `POST /v1/chat/completions` - 主要聊天完成端点
- `GET /health` - 健康检查端点
- `POST /hf/v1/chat/completions` - Hugging Face 兼容端点

## 💬 聊天完成

### 基础请求
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Hello, Claude!"
      }
    ]
  }'
```

### 流式响应
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Tell me a story"
      }
    ],
    "stream": true
  }'
```

### 多轮对话
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "What is the capital of France?"
      },
      {
        "role": "assistant",
        "content": "The capital of France is Paris."
      },
      {
        "role": "user",
        "content": "What is its population?"
      }
    ]
  }'
```

## 🖼️ 图像分析

Claude2API 支持通过 base64 编码图像或 URL 进行图像分析。

### Base64 图像
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What do you see in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
            }
          }
        ]
      }
    ]
  }'
```

### 图像 URL
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "Describe this image"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "https://example.com/image.jpg"
            }
          }
        ]
      }
    ]
  }'
```

## 📁 文件上传支持

对于长上下文或文档分析，Claude2API 支持文件上传。

### 文本文件上传
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Please analyze this document: [当上下文超过 MAX_CHAT_HISTORY_LENGTH 时，文件内容将自动上传]"
      }
    ]
  }'
```

## 🧠 思考过程

通过 `<think>` 标签支持访问 Claude 的逐步推理。

### 带思考的请求
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Solve this math problem step by step: What is 15% of 240?"
      }
    ],
    "thinking": true
  }'
```

## 🔄 高级功能

### 镜像 API（直接使用会话密钥）
当 `ENABLE_MIRROR_API=true` 时，您可以直接使用 Claude 会话密钥：

```bash
curl -X POST http://localhost:8080/mirror/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-ant-sid01-your-session-key" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Hello!"
      }
    ]
  }'
```

### 自定义参数
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-7-sonnet-20250219",
    "messages": [
      {
        "role": "user",
        "content": "Write a creative story"
      }
    ],
    "max_tokens": 1000,
    "temperature": 0.7,
    "stream": true
  }'
```

## 🎯 支持的模型

### 可用模型
- `claude-3-7-sonnet-20250219` - 最新的 Sonnet 模型
- `claude-sonnet-4-20250514` - 所有用户可用
- `claude-3-haiku-20240307` - 快速、轻量级模型
- `claude-3-opus-20240229` - 最强大的模型（仅限 Pro 用户）

### 模型选择
```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "model": "claude-3-haiku-20240307",
    "messages": [
      {
        "role": "user",
        "content": "Quick question: What is 2+2?"
      }
    ]
  }'
```

## 🔧 响应格式

### 标准响应
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "claude-3-7-sonnet-20250219",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

### 流式响应
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-3-7-sonnet-20250219","choices":[{"index":0,"delta":{"content":"Hello"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-3-7-sonnet-20250219","choices":[{"index":0,"delta":{"content":"!"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":1677652288,"model":"claude-3-7-sonnet-20250219","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 🐍 Python 示例

```python
import requests
import json

def chat_with_claude(message, api_key="luoshui123", base_url="http://localhost:8080"):
    url = f"{base_url}/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "model": "claude-3-7-sonnet-20250219",
        "messages": [
            {"role": "user", "content": message}
        ]
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# 使用方法
result = chat_with_claude("Hello, Claude!")
print(result["choices"][0]["message"]["content"])
```

## 🌐 JavaScript 示例

```javascript
async function chatWithClaude(message, apiKey = "luoshui123", baseUrl = "http://localhost:8080") {
    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: "claude-3-7-sonnet-20250219",
            messages: [
                { role: "user", content: message }
            ]
        })
    });
    
    return await response.json();
}

// 使用方法
chatWithClaude("Hello, Claude!")
    .then(result => console.log(result.choices[0].message.content));
```

## ⚠️ 速率限制和最佳实践

### 速率限制
- 请求受 Claude 会话速率限制约束
- 多个会话密钥提供负载均衡
- 失败的请求会自动使用不同会话重试

### 最佳实践
1. **使用适当的模型** - Haiku 用于简单任务，Sonnet 用于复杂任务
2. **在应用程序中实施重试逻辑**
3. **监控令牌使用** 以优化成本
4. **使用流式传输** 获得更好的用户体验
5. **适当时缓存响应**

### 错误处理
```python
import requests

def safe_chat_request(message):
    try:
        response = requests.post(
            "http://localhost:8080/v1/chat/completions",
            headers={"Authorization": "Bearer luoshui123"},
            json={
                "model": "claude-3-7-sonnet-20250219",
                "messages": [{"role": "user", "content": message}]
            },
            timeout=30
        )
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None
```

## 🔍 监控使用情况

### 健康检查
```bash
curl http://localhost:8080/health
```

### 日志分析
监控日志以了解：
- 请求模式
- 错误率
- 响应时间
- 会话密钥使用情况

有关更高级的使用模式和集成示例，请参考项目的 GitHub 仓库。
